const jwt = require("jsonwebtoken");

const GOOGLE_SHEETS_SUBSCRIBERS_ID = '1sdERmtt3I6QDV3Xtw1NzJswQ0MhrbiGg6mf5mTiYlSU';
const GOOGLE_SHEETS_SUBSCRIBERS_PAGE = 'March!A2';
const GOOGLE_SHEETS_SERVICE_ACCOUNT = '115083737441987478188';
*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

async function getGoogleSheetsAccessToken() {
    const iat = Math.floor(Date.now() / 1000)
    const exp = iat + 3600
    const jwtToken = jwt.sign(
        {
            iss: GOOGLE_SHEETS_SERVICE_ACCOUNT,
            scope: "https://www.googleapis.com/auth/spreadsheets",
            aud: "https://accounts.google.com/o/oauth2/token",
            exp,
            iat,
        },
        GOOGLE_SHEETS_PRIVATE_KEY,
        { algorithm: "RS256" },
    )
    const { access_token } = await fetch(
        "https://accounts.google.com/o/oauth2/token",
        {
            method: "POST",
            headers: {
                "Content-Type": "application/x-www-form-urlencoded",
            },
            body: new URLSearchParams({
                grant_type:
                    "urn:ietf:params:oauth:grant-type:jwt-bearer",
                assertion: jwtToken,
            }),
        },
    ).then((response) => response.json())
    return access_token
}

export async function appendDataToGoogleSheet(values) {

    

    const accessToken = await getGoogleSheetsAccessToken();
    const range = GOOGLE_SHEETS_SUBSCRIBERS_PAGE;
    const currentDate = new Date();
    currentDate.setDate(currentDate.getDate() + 4);
    const fourDaysLater = currentDate;
    await fetch(
        `https://sheets.googleapis.com/v4/spreadsheets/${GOOGLE_SHEETS_SUBSCRIBERS_ID}/values/${GOOGLE_SHEETS_SUBSCRIBERS_PAGE}:append?valueInputOption=USER_ENTERED`,
        {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${accessToken}`,
            },
            body: JSON.stringify({
                range,
                values
            }),
        },
    )
}


