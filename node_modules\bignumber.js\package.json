{"name": "bignumber.js", "description": "A library for arbitrary-precision decimal and non-decimal arithmetic", "version": "9.1.2", "keywords": ["arbitrary", "precision", "arithmetic", "big", "number", "decimal", "float", "biginteger", "bigdecimal", "bignumber", "bigint", "bignum"], "repository": {"type": "git", "url": "https://github.com/MikeMcl/bignumber.js.git"}, "main": "bignumber", "module": "bignumber.mjs", "browser": "bignumber.js", "types": "bignumber.d.ts", "exports": {".": {"types": "./bignumber.d.ts", "require": "./bignumber.js", "import": "./bignumber.mjs", "browser": "./bignumber.js"}, "./bignumber.mjs": "./bignumber.mjs", "./bignumber.js": "./bignumber.js", "./package.json": "./package.json"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "engines": {"node": "*"}, "license": "MIT", "scripts": {"test": "node test/test"}, "dependencies": {}}