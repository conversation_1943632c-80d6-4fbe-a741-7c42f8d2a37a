/// <reference types="node" />
import { OAuth2Client, JW<PERSON>, Compute, UserRefreshClient, BaseExternalAccountClient, GaxiosPromise, GoogleConfigurable, MethodOptions, StreamMethodOptions, GlobalOptions, GoogleAuth, BodyResponseCallback, APIRequestContext } from 'googleapis-common';
import { Readable } from 'stream';
export declare namespace apikeys_v2 {
    export interface Options extends GlobalOptions {
        version: 'v2';
    }
    interface StandardParameters {
        /**
         * Auth client or API Key for the request
         */
        auth?: string | OAuth2Client | JWT | Compute | UserRefreshClient | BaseExternalAccountClient | GoogleAuth;
        /**
         * V1 error format.
         */
        '$.xgafv'?: string;
        /**
         * OAuth access token.
         */
        access_token?: string;
        /**
         * Data format for response.
         */
        alt?: string;
        /**
         * JSONP
         */
        callback?: string;
        /**
         * Selector specifying which fields to include in a partial response.
         */
        fields?: string;
        /**
         * API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.
         */
        key?: string;
        /**
         * OAuth 2.0 token for the current user.
         */
        oauth_token?: string;
        /**
         * Returns response with indentations and line breaks.
         */
        prettyPrint?: boolean;
        /**
         * Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.
         */
        quotaUser?: string;
        /**
         * Legacy upload protocol for media (e.g. "media", "multipart").
         */
        uploadType?: string;
        /**
         * Upload protocol for media (e.g. "raw", "multipart").
         */
        upload_protocol?: string;
    }
    /**
     * API Keys API
     *
     * Manages the API keys associated with developer projects.
     *
     * @example
     * ```js
     * const {google} = require('googleapis');
     * const apikeys = google.apikeys('v2');
     * ```
     */
    export class Apikeys {
        context: APIRequestContext;
        keys: Resource$Keys;
        operations: Resource$Operations;
        projects: Resource$Projects;
        constructor(options: GlobalOptions, google?: GoogleConfigurable);
    }
    /**
     * This resource represents a long-running operation that is the result of a network API call.
     */
    export interface Schema$Operation {
        /**
         * If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available.
         */
        done?: boolean | null;
        /**
         * The error result of the operation in case of failure or cancellation.
         */
        error?: Schema$Status;
        /**
         * Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata. Any method that returns a long-running operation should document the metadata type, if any.
         */
        metadata?: {
            [key: string]: any;
        } | null;
        /**
         * The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id\}`.
         */
        name?: string | null;
        /**
         * The normal, successful response of the operation. If the original method returns no data on success, such as `Delete`, the response is `google.protobuf.Empty`. If the original method is standard `Get`/`Create`/`Update`, the response should be the resource. For other methods, the response should have the type `XxxResponse`, where `Xxx` is the original method name. For example, if the original method name is `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.
         */
        response?: {
            [key: string]: any;
        } | null;
    }
    /**
     * The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).
     */
    export interface Schema$Status {
        /**
         * The status code, which should be an enum value of google.rpc.Code.
         */
        code?: number | null;
        /**
         * A list of messages that carry the error details. There is a common set of message types for APIs to use.
         */
        details?: Array<{
            [key: string]: any;
        }> | null;
        /**
         * A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.
         */
        message?: string | null;
    }
    /**
     * Identifier of an Android application for key use.
     */
    export interface Schema$V2AndroidApplication {
        /**
         * The package name of the application.
         */
        packageName?: string | null;
        /**
         * The SHA1 fingerprint of the application. For example, both sha1 formats are acceptable : DA:39:A3:EE:5E:6B:4B:0D:32:55:BF:EF:95:60:18:90:AF:D8:07:09 or DA39A3EE5E6B4B0D3255BFEF95601890AFD80709. Output format is the latter.
         */
        sha1Fingerprint?: string | null;
    }
    /**
     * The Android apps that are allowed to use the key.
     */
    export interface Schema$V2AndroidKeyRestrictions {
        /**
         * A list of Android applications that are allowed to make API calls with this key.
         */
        allowedApplications?: Schema$V2AndroidApplication[];
    }
    /**
     * A restriction for a specific service and optionally one or multiple specific methods. Both fields are case insensitive.
     */
    export interface Schema$V2ApiTarget {
        /**
         * Optional. List of one or more methods that can be called. If empty, all methods for the service are allowed. A wildcard (*) can be used as the last symbol. Valid examples: `google.cloud.translate.v2.TranslateService.GetSupportedLanguage` `TranslateText` `Get*` `translate.googleapis.com.Get*`
         */
        methods?: string[] | null;
        /**
         * The service for this restriction. It should be the canonical service name, for example: `translate.googleapis.com`. You can use [`gcloud services list`](/sdk/gcloud/reference/services/list) to get a list of services that are enabled in the project.
         */
        service?: string | null;
    }
    /**
     * The HTTP referrers (websites) that are allowed to use the key.
     */
    export interface Schema$V2BrowserKeyRestrictions {
        /**
         * A list of regular expressions for the referrer URLs that are allowed to make API calls with this key.
         */
        allowedReferrers?: string[] | null;
    }
    /**
     * Response message for `GetKeyString` method.
     */
    export interface Schema$V2GetKeyStringResponse {
        /**
         * An encrypted and signed value of the key.
         */
        keyString?: string | null;
    }
    /**
     * The iOS apps that are allowed to use the key.
     */
    export interface Schema$V2IosKeyRestrictions {
        /**
         * A list of bundle IDs that are allowed when making API calls with this key.
         */
        allowedBundleIds?: string[] | null;
    }
    /**
     * The representation of a key managed by the API Keys API.
     */
    export interface Schema$V2Key {
        /**
         * Annotations is an unstructured key-value map stored with a policy that may be set by external tools to store and retrieve arbitrary metadata. They are not queryable and should be preserved when modifying objects.
         */
        annotations?: {
            [key: string]: string;
        } | null;
        /**
         * Output only. A timestamp identifying the time this key was originally created.
         */
        createTime?: string | null;
        /**
         * Output only. A timestamp when this key was deleted. If the resource is not deleted, this must be empty.
         */
        deleteTime?: string | null;
        /**
         * Human-readable display name of this key that you can modify. The maximum length is 63 characters.
         */
        displayName?: string | null;
        /**
         * Output only. A checksum computed by the server based on the current value of the Key resource. This may be sent on update and delete requests to ensure the client has an up-to-date value before proceeding. See https://google.aip.dev/154.
         */
        etag?: string | null;
        /**
         * Output only. An encrypted and signed value held by this key. This field can be accessed only through the `GetKeyString` method.
         */
        keyString?: string | null;
        /**
         * Output only. The resource name of the key. The `name` has the form: `projects//locations/global/keys/`. For example: `projects/123456867718/locations/global/keys/b7ff1f9f-8275-410a-94dd-3855ee9b5dd2` NOTE: Key is a global resource; hence the only supported value for location is `global`.
         */
        name?: string | null;
        /**
         * Key restrictions.
         */
        restrictions?: Schema$V2Restrictions;
        /**
         * Output only. Unique id in UUID4 format.
         */
        uid?: string | null;
        /**
         * Output only. A timestamp identifying the time this key was last updated.
         */
        updateTime?: string | null;
    }
    /**
     * Response message for `ListKeys` method.
     */
    export interface Schema$V2ListKeysResponse {
        /**
         * A list of API keys.
         */
        keys?: Schema$V2Key[];
        /**
         * The pagination token for the next page of results.
         */
        nextPageToken?: string | null;
    }
    /**
     * Response message for `LookupKey` method.
     */
    export interface Schema$V2LookupKeyResponse {
        /**
         * The resource name of the API key. If the API key has been purged, resource name is empty.
         */
        name?: string | null;
        /**
         * The project that owns the key with the value specified in the request.
         */
        parent?: string | null;
    }
    /**
     * Describes the restrictions on the key.
     */
    export interface Schema$V2Restrictions {
        /**
         * The Android apps that are allowed to use the key.
         */
        androidKeyRestrictions?: Schema$V2AndroidKeyRestrictions;
        /**
         * A restriction for a specific service and optionally one or more specific methods. Requests are allowed if they match any of these restrictions. If no restrictions are specified, all targets are allowed.
         */
        apiTargets?: Schema$V2ApiTarget[];
        /**
         * The HTTP referrers (websites) that are allowed to use the key.
         */
        browserKeyRestrictions?: Schema$V2BrowserKeyRestrictions;
        /**
         * The iOS apps that are allowed to use the key.
         */
        iosKeyRestrictions?: Schema$V2IosKeyRestrictions;
        /**
         * The IP addresses of callers that are allowed to use the key.
         */
        serverKeyRestrictions?: Schema$V2ServerKeyRestrictions;
    }
    /**
     * The IP addresses of callers that are allowed to use the key.
     */
    export interface Schema$V2ServerKeyRestrictions {
        /**
         * A list of the caller IP addresses that are allowed to make API calls with this key.
         */
        allowedIps?: string[] | null;
    }
    /**
     * Request message for `UndeleteKey` method.
     */
    export interface Schema$V2UndeleteKeyRequest {
    }
    export class Resource$Keys {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Find the parent project and resource name of the API key that matches the key string in the request. If the API key has been purged, resource name will not be set. The service account must have the `apikeys.keys.lookup` permission on the parent project.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        lookupKey(params: Params$Resource$Keys$Lookupkey, options: StreamMethodOptions): GaxiosPromise<Readable>;
        lookupKey(params?: Params$Resource$Keys$Lookupkey, options?: MethodOptions): GaxiosPromise<Schema$V2LookupKeyResponse>;
        lookupKey(params: Params$Resource$Keys$Lookupkey, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        lookupKey(params: Params$Resource$Keys$Lookupkey, options: MethodOptions | BodyResponseCallback<Schema$V2LookupKeyResponse>, callback: BodyResponseCallback<Schema$V2LookupKeyResponse>): void;
        lookupKey(params: Params$Resource$Keys$Lookupkey, callback: BodyResponseCallback<Schema$V2LookupKeyResponse>): void;
        lookupKey(callback: BodyResponseCallback<Schema$V2LookupKeyResponse>): void;
    }
    export interface Params$Resource$Keys$Lookupkey extends StandardParameters {
        /**
         * Required. Finds the project that owns the key string value.
         */
        keyString?: string;
    }
    export class Resource$Operations {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Operations$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Operations$Get, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        get(params: Params$Resource$Operations$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Operations$Get, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        get(params: Params$Resource$Operations$Get, callback: BodyResponseCallback<Schema$Operation>): void;
        get(callback: BodyResponseCallback<Schema$Operation>): void;
    }
    export interface Params$Resource$Operations$Get extends StandardParameters {
        /**
         * The name of the operation resource.
         */
        name?: string;
    }
    export class Resource$Projects {
        context: APIRequestContext;
        locations: Resource$Projects$Locations;
        constructor(context: APIRequestContext);
    }
    export class Resource$Projects$Locations {
        context: APIRequestContext;
        keys: Resource$Projects$Locations$Keys;
        constructor(context: APIRequestContext);
    }
    export class Resource$Projects$Locations$Keys {
        context: APIRequestContext;
        constructor(context: APIRequestContext);
        /**
         * Creates a new API key. NOTE: Key is a global resource; hence the only supported value for location is `global`.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        create(params: Params$Resource$Projects$Locations$Keys$Create, options: StreamMethodOptions): GaxiosPromise<Readable>;
        create(params?: Params$Resource$Projects$Locations$Keys$Create, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        create(params: Params$Resource$Projects$Locations$Keys$Create, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        create(params: Params$Resource$Projects$Locations$Keys$Create, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        create(params: Params$Resource$Projects$Locations$Keys$Create, callback: BodyResponseCallback<Schema$Operation>): void;
        create(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Deletes an API key. Deleted key can be retrieved within 30 days of deletion. Afterward, key will be purged from the project. NOTE: Key is a global resource; hence the only supported value for location is `global`.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        delete(params: Params$Resource$Projects$Locations$Keys$Delete, options: StreamMethodOptions): GaxiosPromise<Readable>;
        delete(params?: Params$Resource$Projects$Locations$Keys$Delete, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        delete(params: Params$Resource$Projects$Locations$Keys$Delete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        delete(params: Params$Resource$Projects$Locations$Keys$Delete, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(params: Params$Resource$Projects$Locations$Keys$Delete, callback: BodyResponseCallback<Schema$Operation>): void;
        delete(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Gets the metadata for an API key. The key string of the API key isn't included in the response. NOTE: Key is a global resource; hence the only supported value for location is `global`.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        get(params: Params$Resource$Projects$Locations$Keys$Get, options: StreamMethodOptions): GaxiosPromise<Readable>;
        get(params?: Params$Resource$Projects$Locations$Keys$Get, options?: MethodOptions): GaxiosPromise<Schema$V2Key>;
        get(params: Params$Resource$Projects$Locations$Keys$Get, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        get(params: Params$Resource$Projects$Locations$Keys$Get, options: MethodOptions | BodyResponseCallback<Schema$V2Key>, callback: BodyResponseCallback<Schema$V2Key>): void;
        get(params: Params$Resource$Projects$Locations$Keys$Get, callback: BodyResponseCallback<Schema$V2Key>): void;
        get(callback: BodyResponseCallback<Schema$V2Key>): void;
        /**
         * Get the key string for an API key. NOTE: Key is a global resource; hence the only supported value for location is `global`.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        getKeyString(params: Params$Resource$Projects$Locations$Keys$Getkeystring, options: StreamMethodOptions): GaxiosPromise<Readable>;
        getKeyString(params?: Params$Resource$Projects$Locations$Keys$Getkeystring, options?: MethodOptions): GaxiosPromise<Schema$V2GetKeyStringResponse>;
        getKeyString(params: Params$Resource$Projects$Locations$Keys$Getkeystring, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        getKeyString(params: Params$Resource$Projects$Locations$Keys$Getkeystring, options: MethodOptions | BodyResponseCallback<Schema$V2GetKeyStringResponse>, callback: BodyResponseCallback<Schema$V2GetKeyStringResponse>): void;
        getKeyString(params: Params$Resource$Projects$Locations$Keys$Getkeystring, callback: BodyResponseCallback<Schema$V2GetKeyStringResponse>): void;
        getKeyString(callback: BodyResponseCallback<Schema$V2GetKeyStringResponse>): void;
        /**
         * Lists the API keys owned by a project. The key string of the API key isn't included in the response. NOTE: Key is a global resource; hence the only supported value for location is `global`.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        list(params: Params$Resource$Projects$Locations$Keys$List, options: StreamMethodOptions): GaxiosPromise<Readable>;
        list(params?: Params$Resource$Projects$Locations$Keys$List, options?: MethodOptions): GaxiosPromise<Schema$V2ListKeysResponse>;
        list(params: Params$Resource$Projects$Locations$Keys$List, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        list(params: Params$Resource$Projects$Locations$Keys$List, options: MethodOptions | BodyResponseCallback<Schema$V2ListKeysResponse>, callback: BodyResponseCallback<Schema$V2ListKeysResponse>): void;
        list(params: Params$Resource$Projects$Locations$Keys$List, callback: BodyResponseCallback<Schema$V2ListKeysResponse>): void;
        list(callback: BodyResponseCallback<Schema$V2ListKeysResponse>): void;
        /**
         * Patches the modifiable fields of an API key. The key string of the API key isn't included in the response. NOTE: Key is a global resource; hence the only supported value for location is `global`.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        patch(params: Params$Resource$Projects$Locations$Keys$Patch, options: StreamMethodOptions): GaxiosPromise<Readable>;
        patch(params?: Params$Resource$Projects$Locations$Keys$Patch, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        patch(params: Params$Resource$Projects$Locations$Keys$Patch, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        patch(params: Params$Resource$Projects$Locations$Keys$Patch, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(params: Params$Resource$Projects$Locations$Keys$Patch, callback: BodyResponseCallback<Schema$Operation>): void;
        patch(callback: BodyResponseCallback<Schema$Operation>): void;
        /**
         * Undeletes an API key which was deleted within 30 days. NOTE: Key is a global resource; hence the only supported value for location is `global`.
         *
         * @param params - Parameters for request
         * @param options - Optionally override request options, such as `url`, `method`, and `encoding`.
         * @param callback - Optional callback that handles the response.
         * @returns A promise if used with async/await, or void if used with a callback.
         */
        undelete(params: Params$Resource$Projects$Locations$Keys$Undelete, options: StreamMethodOptions): GaxiosPromise<Readable>;
        undelete(params?: Params$Resource$Projects$Locations$Keys$Undelete, options?: MethodOptions): GaxiosPromise<Schema$Operation>;
        undelete(params: Params$Resource$Projects$Locations$Keys$Undelete, options: StreamMethodOptions | BodyResponseCallback<Readable>, callback: BodyResponseCallback<Readable>): void;
        undelete(params: Params$Resource$Projects$Locations$Keys$Undelete, options: MethodOptions | BodyResponseCallback<Schema$Operation>, callback: BodyResponseCallback<Schema$Operation>): void;
        undelete(params: Params$Resource$Projects$Locations$Keys$Undelete, callback: BodyResponseCallback<Schema$Operation>): void;
        undelete(callback: BodyResponseCallback<Schema$Operation>): void;
    }
    export interface Params$Resource$Projects$Locations$Keys$Create extends StandardParameters {
        /**
         * User specified key id (optional). If specified, it will become the final component of the key resource name. The id must be unique within the project, must conform with RFC-1034, is restricted to lower-cased letters, and has a maximum length of 63 characters. In another word, the id must match the regular expression: `[a-z]([a-z0-9-]{0,61\}[a-z0-9])?`. The id must NOT be a UUID-like string.
         */
        keyId?: string;
        /**
         * Required. The project in which the API key is created.
         */
        parent?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$V2Key;
    }
    export interface Params$Resource$Projects$Locations$Keys$Delete extends StandardParameters {
        /**
         * Optional. The etag known to the client for the expected state of the key. This is to be used for optimistic concurrency.
         */
        etag?: string;
        /**
         * Required. The resource name of the API key to be deleted.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Keys$Get extends StandardParameters {
        /**
         * Required. The resource name of the API key to get.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Keys$Getkeystring extends StandardParameters {
        /**
         * Required. The resource name of the API key to be retrieved.
         */
        name?: string;
    }
    export interface Params$Resource$Projects$Locations$Keys$List extends StandardParameters {
        /**
         * Optional. Specifies the maximum number of results to be returned at a time.
         */
        pageSize?: number;
        /**
         * Optional. Requests a specific page of results.
         */
        pageToken?: string;
        /**
         * Required. Lists all API keys associated with this project.
         */
        parent?: string;
        /**
         * Optional. Indicate that keys deleted in the past 30 days should also be returned.
         */
        showDeleted?: boolean;
    }
    export interface Params$Resource$Projects$Locations$Keys$Patch extends StandardParameters {
        /**
         * Output only. The resource name of the key. The `name` has the form: `projects//locations/global/keys/`. For example: `projects/123456867718/locations/global/keys/b7ff1f9f-8275-410a-94dd-3855ee9b5dd2` NOTE: Key is a global resource; hence the only supported value for location is `global`.
         */
        name?: string;
        /**
         * The field mask specifies which fields to be updated as part of this request. All other fields are ignored. Mutable fields are: `display_name`, `restrictions`, and `annotations`. If an update mask is not provided, the service treats it as an implied mask equivalent to all allowed fields that are set on the wire. If the field mask has a special value "*", the service treats it equivalent to replace all allowed mutable fields.
         */
        updateMask?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$V2Key;
    }
    export interface Params$Resource$Projects$Locations$Keys$Undelete extends StandardParameters {
        /**
         * Required. The resource name of the API key to be undeleted.
         */
        name?: string;
        /**
         * Request body metadata
         */
        requestBody?: Schema$V2UndeleteKeyRequest;
    }
    export {};
}
