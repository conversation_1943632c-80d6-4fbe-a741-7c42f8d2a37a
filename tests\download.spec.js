const fs = require("fs");
const path = require("path");
const AdmZip = require("adm-zip");
const { test, expect } = require("@playwright/test");
const { folderManager } = require("../util/folderManager");
require("dotenv").config();

// Configuration
const INCOMING_DIR = process.env.INCOMING_DIR || "./incoming";
const TE_LOGIN_URL =
  process.env.TE_LOGIN_URL || "https://production.jow.medknow.com/login";
const TE_MYTASK_URL =
  process.env.TE_MYTASK_URL || "https://production.jow.medknow.com/mytask";
const USERNAME = process.env.TE_USERNAME || "<EMAIL>";
const PASSWORD = process.env.TE_PASSWORD || "Editing@1234";
const TARGET_DATE =
  process.env.TARGET_DATE ||
  new Date().toLocaleDateString("en-GB").split("/").join("-");

// Ensure incoming directory exists
if (!fs.existsSync(INCOMING_DIR)) {
  fs.mkdirSync(INCOMING_DIR, { recursive: true });
}

// Setup date folder
const DATE_DIR = path.join(INCOMING_DIR, TARGET_DATE);
if (!fs.existsSync(DATE_DIR)) {
  fs.mkdirSync(DATE_DIR, { recursive: true });
}

// Clean up any stale locks from previous runs
folderManager.cleanupStaleLocks();

// BATCH_DIR will be created dynamically in beforeAll to prevent race conditions
let BATCH_DIR;

test.describe.configure({ mode: "serial" });

test.describe("TE Download Phase", () => {
  let page;
  let articlesToDownload = [];
  let batchSummary = [];

  test.beforeAll(async ({ browser }) => {
    // Create batch directory safely to prevent race conditions
    BATCH_DIR = await folderManager.getSingletonBatchDir(DATE_DIR);
    console.log(`📁 Using batch directory: ${BATCH_DIR}`);

    const context = await browser.newContext({ downloadsPath: "./downloads" });
    page = await context.newPage();

    // Login
    await page.goto(TE_LOGIN_URL);
    await page
      .getByRole("textbox", { name: "User ID / Email ID" })
      .fill(USERNAME);
    await page.getByPlaceholder("Password").fill(PASSWORD);
    await page.getByRole("link", { name: "Login" }).click();

    console.log("✅ Successfully logged into TE portal");
  });

  test('Discover articles with "Yet-to-Start" status', async () => {
    await page.goto(TE_MYTASK_URL);
    await page.waitForLoadState();

    await page.locator("#mytaskTable_length select").selectOption("100");

    console.log(`🗓️ Searching for articles with date: ${TARGET_DATE}`);
    await page.getByLabel("Search:").fill(TARGET_DATE);

    await page
      .getByLabel("Schedule Start Date: activate to sort column ascending")
      .click();
    await page
      .getByLabel("Schedule Start Date: activate to sort column descending")
      .click();

    const rows = await page.locator("#mytaskTable tbody tr");
    const rowCount = await rows.count();

    console.log(`📊 Found ${rowCount} rows in task table`);

    for (let i = 0; i < rowCount; i++) {
      const columns = rows.nth(i).locator("td");
      const scheduleStartDate = (await columns.nth(6).innerText()).trim();

      if (scheduleStartDate === TARGET_DATE) {
        const status = (await columns.nth(5).innerText()).trim();
        if (status !== "Yet-to-Start") {
          const journalId = (await columns.nth(1).innerText()).trim();
          const articleId = (await columns.nth(3).innerText()).trim();

          articlesToDownload.push({
            articleId,
            journal: journalId,
            downloadDate: new Date().toISOString(),
          });
        }
      }
    }

    console.log(
      `🎯 Found ${articlesToDownload.length} articles with "Yet-to-Start" status:`
    );
    articlesToDownload.forEach((a) =>
      console.log(`- ${a.articleId} (${a.journal})`)
    );

    expect(articlesToDownload.length).toBeGreaterThan(0);
  });

  test("Download and process all articles", async () => {
    console.log(
      `📥 Starting download phase for ${articlesToDownload.length} articles`
    );

    let successCount = 0;
    let errorCount = 0;

    for (const article of articlesToDownload) {
      try {
        const metadata = await downloadAndProcessArticle(page, article);
        batchSummary.push(metadata);
        if (metadata.status === "success") {
          successCount++;
        } else {
          errorCount++;
        }
      } catch (error) {
        console.error(
          `❌ Failed to process ${article.articleId}:`,
          error.message
        );
        errorCount++;
      }
    }

    const summaryPath = path.join(BATCH_DIR, "batch_summary.json");
    const summaryObj = {
      target_date: TARGET_DATE,
      batch_number: path.basename(BATCH_DIR),
      total_articles: articlesToDownload.length,
      success_count: successCount,
      error_count: errorCount,
      processed_at: new Date().toISOString(),
      articles: batchSummary,
    };

    fs.writeFileSync(summaryPath, JSON.stringify(summaryObj, null, 2));

    console.log(`\n📊 Batch Summary saved: ${summaryPath}`);
    expect(successCount).toBeGreaterThan(0);
  });
});

/**
 * Download + process one article
 */
async function downloadAndProcessArticle(page, article) {
  const { articleId, journal, downloadDate } = article;
  const zipPath = path.join(BATCH_DIR, `${articleId}.zip`);
  const jsonPath = path.join(BATCH_DIR, `${articleId}.json`);

  console.log(`\n🔄 Processing article: ${articleId}`);

  try {
    await page.goto(TE_MYTASK_URL);
    await page.waitForLoadState();

    await page.getByLabel("Search:").fill(articleId);
    await page.waitForLoadState("networkidle");
    await page.waitForTimeout(2000);
    await expect(page.getByText(/Showing 1 to 1/)).toBeVisible();

    await page.getByRole("link", { name: "Start" }).click();
    await page.waitForLoadState();

    const authors = await extractAuthorDetails(page);

    // Normal download
    const downloadPromise = page.waitForEvent("download");
    await page.getByRole("link", { name: " Download" }).click();
    await page.waitForTimeout(1000);
    // 🚫 File Not Found check
    const fileNotFound = await page.getByText("File Not Found").count();
    if (fileNotFound > 0) {
      console.log(`🚫 File not found: ${articleId}`);

      const errorMetadata = {
         article_id: articleId,
        journal,
        download_date: downloadDate,
        files: { zip_path: null, error: "file_not_found" },
        authors,
        status: "file_not_found",
        source: "TE_portal",
        log: ["Portal displayed 'File Not Found'"],
      };

      fs.writeFileSync(jsonPath, JSON.stringify(errorMetadata, null, 2));
      return errorMetadata;
    }
    try {
      const download = await downloadPromise;
      await download.saveAs(zipPath);
      console.log(`📦 Downloaded ZIP: ${zipPath}`);
    } catch (downloadError) {
      console.log(
        "Error downloading the file:",
        downloadError.message,
        articleId
      );
    }
    
    const metadata = await createArticleMetadata(
      articleId,
      journal,
      downloadDate,
      zipPath,
      authors
    );
    fs.writeFileSync(jsonPath, JSON.stringify(metadata, null, 2));
    console.log(`📄 Created metadata: ${jsonPath}`);
    
    await page.getByRole("link", { name: "Cancel" }).click();
    console.log(`✅ Successfully processed: ${articleId}`);

    return metadata;
  } catch (error) {
    console.error(`❌ Error processing ${articleId}: ${error.message}`);

    const errorMetadata = {
      article_id: articleId,
      journal,
      download_date: downloadDate,
      files: {
        zip_path: `${TARGET_DATE}/${path.basename(BATCH_DIR)}/${articleId}.zip`,
        error: "processing_failed",
      },
      authors: [],
      status: "error",
      source: "TE_portal",
      log: [`Error: ${error.message}`],
    };

    fs.writeFileSync(jsonPath, JSON.stringify(errorMetadata, null, 2));
    return errorMetadata;
  }
}

async function extractAuthorDetails(page) {
  const authors = [];
  try {
    await page
      .getByRole("button", { name: "Author Details" })
      .click({ force: true });
    await expect(
      page.getByRole("heading", { name: "Author Details" })
    ).toBeVisible();

    const tableElement = page.locator("#articleCommentTable tbody");
    const rowCount = await tableElement.locator("tr").count();

    for (let i = 0; i < rowCount; i++) {
      const columns = tableElement.locator("tr").nth(i).locator("td");
      authors.push({
        name: (await columns.nth(1).innerText()).trim(),
        email: (await columns.nth(2).innerText()).trim(),
        affiliation: (await columns.nth(3).innerText()).trim(),
        copyright_status: (await columns.nth(6).innerText()).trim(),
      });
    }
    await page.getByRole("button", { name: "×" }).click();
  } catch (err) {
    console.warn(`⚠️ Could not extract author details: ${err.message}`);
  }
  return authors;
}

async function createArticleMetadata(
  articleId,
  journal,
  downloadDate,
  zipPath,
  authors
) {
  const metadata = {
    article_id: articleId,
    journal,
    download_date: downloadDate,
    files: {
      zip_path: `${TARGET_DATE}/${path.basename(BATCH_DIR)}/${articleId}.zip`,
      manuscript_file: "missing",
      fp_file: "missing",
    },
    authors,
    status: "success",
    source: "TE_portal",
    log: [],
  };

  try {
    const zipContents = await analyzeZipContents(zipPath);
    metadata.files.manuscript_file = zipContents.manuscriptFile || "missing";
    metadata.files.fp_file = zipContents.fpFile || "missing";

    if (zipContents.error) {
      metadata.files.error = zipContents.error;
      metadata.log.push(`ZIP analysis error: ${zipContents.error}`);
    }
  } catch (err) {
    metadata.files.error = "corrupt zip";
    metadata.log.push(`ZIP corruption error: ${err.message}`);
  }
  return metadata;
}

async function analyzeZipContents(zipPath) {
  try {
    const zip = new AdmZip(zipPath);
    const zipEntries = zip.getEntries();

    let manuscriptFile = null;
    let fpFile = null;

    zipEntries.forEach((entry) => {
      const f = entry.entryName.toLowerCase();
      if (
        f.includes("manuscript") &&
        (f.endsWith(".docx") || f.endsWith(".doc"))
      )
        manuscriptFile = entry.entryName;
      if (f.includes("fp") && (f.endsWith(".docx") || f.endsWith(".doc")))
        fpFile = entry.entryName;
    });

    return {
      manuscriptFile,
      fpFile,
      totalFiles: zipEntries.length,
      error: null,
    };
  } catch (err) {
    return {
      manuscriptFile: null,
      fpFile: null,
      totalFiles: 0,
      error: err.message,
    };
  }
}
