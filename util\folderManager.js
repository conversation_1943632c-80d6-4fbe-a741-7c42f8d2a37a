const fs = require("fs");
const path = require("path");

/**
 * Thread-safe folder manager to prevent race conditions during parallel test execution
 */
class FolderManager {
  constructor() {
    this.lockFile = path.join(__dirname, ".folder-lock");
    this.maxRetries = 10;
    this.retryDelay = 100; // milliseconds
  }

  /**
   * Acquire a lock to prevent race conditions
   */
  async acquireLock() {
    for (let i = 0; i < this.maxRetries; i++) {
      try {
        // Try to create lock file exclusively
        fs.writeFileSync(this.lockFile, process.pid.toString(), { flag: 'wx' });
        return true;
      } catch (error) {
        if (error.code === 'EEXIST') {
          // Lock file exists, wait and retry
          await this.sleep(this.retryDelay);
          continue;
        }
        throw error;
      }
    }
    throw new Error('Could not acquire folder lock after maximum retries');
  }

  /**
   * Release the lock
   */
  releaseLock() {
    try {
      if (fs.existsSync(this.lockFile)) {
        fs.unlinkSync(this.lockFile);
      }
    } catch (error) {
      // Ignore errors when releasing lock
      console.warn('Warning: Could not release folder lock:', error.message);
    }
  }

  /**
   * Sleep for specified milliseconds
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Thread-safe method to get the next batch directory
   * This prevents race conditions when multiple workers try to create folders simultaneously
   */
  async getNextBatchDir(baseDir) {
    await this.acquireLock();
    
    try {
      // Ensure base directory exists
      if (!fs.existsSync(baseDir)) {
        fs.mkdirSync(baseDir, { recursive: true });
      }

      // Find existing directories
      const existing = fs
        .readdirSync(baseDir)
        .filter((f) => {
          const fullPath = path.join(baseDir, f);
          return fs.statSync(fullPath).isDirectory();
        });

      // Get numeric directories and find next number
      const nums = existing
        .map((f) => parseInt(f))
        .filter((n) => !isNaN(n));
      
      const next = nums.length > 0 ? Math.max(...nums) + 1 : 1;
      const batchDir = path.join(baseDir, String(next));

      // Create the directory
      fs.mkdirSync(batchDir, { recursive: true });
      
      return batchDir;
    } finally {
      this.releaseLock();
    }
  }

  /**
   * Get or create a singleton batch directory for the current test run
   * This ensures only one batch directory is created per date, regardless of parallel execution
   */
  async getSingletonBatchDir(baseDir) {
    const singletonDir = path.join(baseDir, "1");
    
    await this.acquireLock();
    
    try {
      // Ensure base directory exists
      if (!fs.existsSync(baseDir)) {
        fs.mkdirSync(baseDir, { recursive: true });
      }

      // Create singleton directory if it doesn't exist
      if (!fs.existsSync(singletonDir)) {
        fs.mkdirSync(singletonDir, { recursive: true });
      }
      
      return singletonDir;
    } finally {
      this.releaseLock();
    }
  }

  /**
   * Safely create download directory structure for test files
   * This prevents race conditions when multiple tests try to create the same directory
   */
  async ensureDownloadDir(basePath, ...pathSegments) {
    const fullPath = path.join(basePath, ...pathSegments);

    await this.acquireLock();

    try {
      if (!fs.existsSync(fullPath)) {
        fs.mkdirSync(fullPath, { recursive: true });
      }
      return fullPath;
    } finally {
      this.releaseLock();
    }
  }

  /**
   * Clean up any stale lock files (call this during setup)
   */
  cleanupStaleLocks() {
    try {
      if (fs.existsSync(this.lockFile)) {
        const lockContent = fs.readFileSync(this.lockFile, 'utf8');
        const lockPid = parseInt(lockContent);

        // Check if the process is still running
        try {
          process.kill(lockPid, 0); // Signal 0 just checks if process exists
        } catch (error) {
          if (error.code === 'ESRCH') {
            // Process doesn't exist, remove stale lock
            fs.unlinkSync(this.lockFile);
            console.log('Removed stale folder lock file');
          }
        }
      }
    } catch (error) {
      // Ignore errors during cleanup
      console.warn('Warning during lock cleanup:', error.message);
    }
  }
}

// Export singleton instance
const folderManager = new FolderManager();

module.exports = {
  FolderManager,
  folderManager
};
