@echo off
setlocal enabledelayedexpansion

:: Set console colors and title
title TE Automation - Download Phase
color 0A

echo.
echo ========================================
echo    TE AUTOMATION - DOWNLOAD PHASE
echo ========================================
echo.

:: Get current date in DD-MM-YYYY format
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YY=%dt:~2,2%" & set "YYYY=%dt:~0,4%" & set "MM=%dt:~4,2%" & set "DD=%dt:~6,2%"
set "current_date=%DD%-%MM%-%YYYY%"

echo Current date: %current_date%
echo.
echo Please enter the target date for article download:
echo Format: DD-MM-YYYY (e.g., 15-09-2025)
echo.
echo Press ENTER to use today's date (%current_date%)
echo Or type a specific date:
echo.

set /p "user_date=Target Date: "

:: Use current date if user pressed enter without input
if "%user_date%"=="" (
    set "target_date=%current_date%"
    echo Using today's date: %target_date%
) else (
    set "target_date=%user_date%"
    echo Using specified date: %target_date%
)

echo.

:: Validate date format (basic validation)
echo %target_date% | findstr /r "^[0-9][0-9]-[0-9][0-9]-[0-9][0-9][0-9][0-9]$" >nul
if errorlevel 1 (
    echo.
    echo ❌ ERROR: Invalid date format!
    echo Please use DD-MM-YYYY format (e.g., 15-09-2025)
    echo.
    pause
    exit /b 1
)

echo.
echo ========================================
echo    STARTING DOWNLOAD AUTOMATION
echo ========================================
echo.
echo 📅 Target Date: %target_date%
echo 🤖 Running Playwright UI mode...
echo.
echo Instructions:
echo 1. The Playwright UI will open
echo 2. Click on "tests/download.spec.js" 
echo 3. Click the "Run" button to start
echo 4. Monitor the progress in the UI
echo.

:: Set the TARGET_DATE environment variable
set TARGET_DATE=%target_date%

:: Create or update .env file with the target date
echo # TE Automation Environment Configuration > .env
echo # Auto-generated by run-download.bat >> .env
echo. >> .env
echo # TE Portal Credentials >> .env
echo TE_USERNAME=<EMAIL> >> .env
echo TE_PASSWORD=Editing@1234 >> .env
echo. >> .env
echo # TE Portal URLs >> .env
echo TE_LOGIN_URL=https://production.jow.medknow.com/login >> .env
echo TE_MYTASK_URL=https://production.jow.medknow.com/mytask >> .env
echo. >> .env
echo # Directory Configuration >> .env
echo INCOMING_DIR=./incoming >> .env
echo DOWNLOADS_DIR=./downloads >> .env
echo. >> .env
echo # Target Date (Auto-set by batch script) >> .env
echo TARGET_DATE=%target_date% >> .env

echo ✅ Environment configured with target date: %target_date%
echo.

:: Check if node_modules exists
if not exist "node_modules" (
    echo ⚠️  Node modules not found. Installing dependencies...
    echo.
    call npm install
    if errorlevel 1 (
        echo.
        echo ❌ ERROR: Failed to install dependencies!
        echo Please run 'npm install' manually.
        echo.
        pause
        exit /b 1
    )
    echo ✅ Dependencies installed successfully!
    echo.
)

:: Run Playwright in UI mode
echo 🚀 Launching Playwright UI...
echo.

call npx playwright test tests/download.spec.js --ui

:: Check if the command was successful
if errorlevel 1 (
    echo.
    echo ❌ ERROR: Playwright execution failed!
    echo.
    echo Troubleshooting tips:
    echo 1. Make sure you have Node.js installed
    echo 2. Run 'npm install' to install dependencies
    echo 3. Check if the TE portal is accessible
    echo 4. Verify your credentials in the .env file
    echo.
) else (
    echo.
    echo ✅ Playwright UI session completed!
    echo.
    echo 📁 Check the 'incoming' folder for downloaded articles:
    echo    incoming\%target_date%
    echo.
)

echo.
echo ========================================
echo           SESSION COMPLETE
echo ========================================
echo.
pause
