{"version": 3, "file": "common.js", "sourceRoot": "", "sources": ["../../src/common.ts"], "names": [], "mappings": ";AAAA,4BAA4B;AAC5B,kEAAkE;AAClE,mEAAmE;AACnE,0CAA0C;AAC1C,EAAE;AACF,gDAAgD;AAChD,EAAE;AACF,sEAAsE;AACtE,oEAAoE;AACpE,2EAA2E;AAC3E,sEAAsE;AACtE,iCAAiC;;;AAGjC,6BAAwB;AAExB,uDAAuD;AAEvD,MAAa,WAAqB,SAAQ,KAAK;IAkB7C,YACE,OAAe,EACR,MAAqB,EACrB,QAA4B,EAC5B,KAAqC;QAE5C,KAAK,CAAC,OAAO,CAAC,CAAC;QAJR,WAAM,GAAN,MAAM,CAAe;QACrB,aAAQ,GAAR,QAAQ,CAAoB;QAC5B,UAAK,GAAL,KAAK,CAAgC;QAI5C,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,IAAI;gBACF,IAAI,CAAC,QAAQ,CAAC,IAAI,GAAG,aAAa,CAAC,MAAM,CAAC,YAAY,EAAE,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,IAAI,CAAC,CAAC;aACzE;YAAC,WAAM;gBACN,qDAAqD;gBACrD,oEAAoE;gBACpE,0DAA0D;aAC3D;YAED,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;SACpC;QAED,IAAI,KAAK,IAAI,MAAM,IAAI,KAAK,IAAI,KAAK,CAAC,IAAI,EAAE;YAC1C,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;SACxB;QAED,IAAI,MAAM,CAAC,aAAa,EAAE;YACxB,MAAM,aAAa,GAAG,CAAA,MAAM,CAAC,aAAgB,CAAA,CAAC;YAE9C,sDAAsD;YACtD,+CAA+C;YAC/C,IAAI,CAAC,MAAM,GAAG,EAAC,GAAG,MAAM,EAAC,CAAC;YAC1B,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACjB,4DAA4D;gBAC5D,IAAI,CAAC,QAAQ,GAAG,EAAC,GAAG,IAAI,CAAC,QAAQ,EAAE,MAAM,EAAE,EAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAC,EAAC,CAAC;aACvE;YAED,MAAM,OAAO,GAAG,aAAa,CAAC,EAAC,MAAM,EAAE,QAAQ,EAAC,CAAC,CAAC;YAClD,IAAI,CAAC,MAAM,GAAG,EAAC,GAAG,MAAM,EAAE,GAAG,OAAO,CAAC,MAAM,EAAC,CAAC;YAE7C,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACjB,IAAI,CAAC,QAAQ,GAAG,EAAC,GAAG,IAAI,CAAC,QAAQ,EAAE,GAAG,OAAO,CAAC,QAAQ,EAAE,MAAM,EAAC,CAAC;aACjE;SACF;IACH,CAAC;CACF;AA7DD,kCA6DC;AA2MD,SAAS,aAAa,CAAC,YAAgC,EAAE,IAAS;IAChE,QAAQ,YAAY,EAAE;QACpB,KAAK,QAAQ;YACX,OAAO,IAAI,CAAC;QACd,KAAK,MAAM;YACT,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;QAC1C,KAAK,aAAa;YAChB,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;QACxD,KAAK,MAAM;YACT,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;QACjC;YACE,OAAO,IAAI,CAAC;KACf;AACH,CAAC;AAED;;;;;;;GAOG;AACH,SAAgB,oBAAoB,CAAU,IAG7C;IACC,MAAM,MAAM,GACV,0EAA0E,CAAC;IAE7E,SAAS,aAAa,CAAC,OAAiB;QACtC,IAAI,CAAC,OAAO;YAAE,OAAO;QAErB,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;YACtC,iCAAiC;YACjC,IAAI,kBAAkB,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;gBAChC,OAAO,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC;aACvB;SACF;IACH,CAAC;IAED,SAAS,YAAY,CAAC,GAAkB,EAAE,GAAwB;QAChE,IACE,OAAO,GAAG,KAAK,QAAQ;YACvB,GAAG,KAAK,IAAI;YACZ,OAAO,GAAG,CAAC,GAAG,CAAC,KAAK,QAAQ,EAC5B;YACA,MAAM,IAAI,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;YAEtB,IAAI,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;gBACvD,GAAG,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC;aACnB;SACF;IACH,CAAC;IAED,SAAS,YAAY,CAAkC,GAAM;QAC3D,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,GAAG,KAAK,IAAI,EAAE;YAC3C,IAAI,YAAY,IAAI,GAAG,EAAE;gBACvB,GAAG,CAAC,YAAY,CAAC,GAAG,MAAM,CAAC;aAC5B;YAED,IAAI,WAAW,IAAI,GAAG,EAAE;gBACtB,GAAG,CAAC,WAAW,CAAC,GAAG,MAAM,CAAC;aAC3B;SACF;IACH,CAAC;IAED,IAAI,IAAI,CAAC,MAAM,EAAE;QACf,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAEnC,YAAY,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAClC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAE/B,YAAY,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAClC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAE/B,IAAI;YACF,MAAM,GAAG,GAAG,IAAI,SAAG,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,IAAI,EAAE,CAAC,CAAC;YAC3C,IAAI,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;gBACjC,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;aACvC;YAED,IAAI,CAAC,MAAM,CAAC,GAAG,GAAG,GAAG,CAAC,QAAQ,EAAE,CAAC;SAClC;QAAC,WAAM;YACN,iDAAiD;SAClD;KACF;IAED,IAAI,IAAI,CAAC,QAAQ,EAAE;QACjB,oBAAoB,CAAC,EAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAC,CAAC,CAAC;QACrD,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;QAErC,YAAY,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QACpC,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;KAClC;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AA1ED,oDA0EC"}