# TE Automation - Download Phase Runner
# PowerShell version for better compatibility

# Set console properties
$Host.UI.RawUI.WindowTitle = "TE Automation - Download Phase"
Clear-Host

# Function to display colored text
function Write-ColorText {
    param(
        [string]$Text,
        [string]$Color = "White"
    )
    Write-Host $Text -ForegroundColor $Color
}

# Function to validate date format
function Test-DateFormat {
    param([string]$DateString)
    
    if ($DateString -match '^\d{2}-\d{2}-\d{4}$') {
        try {
            $parts = $DateString.Split('-')
            $day = [int]$parts[0]
            $month = [int]$parts[1] 
            $year = [int]$parts[2]
            
            # Basic validation
            if ($day -ge 1 -and $day -le 31 -and $month -ge 1 -and $month -le 12 -and $year -ge 2020) {
                return $true
            }
        }
        catch {
            return $false
        }
    }
    return $false
}

Write-Host ""
Write-ColorText "========================================" "Cyan"
Write-ColorText "    TE AUTOMATION - DOWNLOAD PHASE" "Cyan"
Write-ColorText "========================================" "Cyan"
Write-Host ""

# Get current date in DD-MM-YYYY format
$currentDate = Get-Date -Format "dd-MM-yyyy"

Write-ColorText "Current date: $currentDate" "Yellow"
Write-Host ""
Write-Host "Please enter the target date for article download:"
Write-ColorText "Format: DD-MM-YYYY (e.g., 15-09-2025)" "Gray"
Write-Host ""
Write-Host "Press ENTER to use today's date ($currentDate)"
Write-Host "Or type a specific date:"
Write-Host ""

$userDate = Read-Host "Target Date"

# Use current date if user pressed enter without input
if ([string]::IsNullOrWhiteSpace($userDate)) {
    $targetDate = $currentDate
    Write-ColorText "Using today's date: $targetDate" "Green"
} else {
    $targetDate = $userDate.Trim()
    Write-ColorText "Using specified date: $targetDate" "Green"
}

Write-Host ""

# Validate date format
if (-not (Test-DateFormat $targetDate)) {
    Write-Host ""
    Write-ColorText "❌ ERROR: Invalid date format!" "Red"
    Write-Host "Please use DD-MM-YYYY format (e.g., 15-09-2025)"
    Write-Host ""
    Read-Host "Press ENTER to exit"
    exit 1
}

Write-Host ""
Write-ColorText "========================================" "Cyan"
Write-ColorText "    STARTING DOWNLOAD AUTOMATION" "Cyan"
Write-ColorText "========================================" "Cyan"
Write-Host ""
Write-ColorText "📅 Target Date: $targetDate" "Yellow"
Write-ColorText "🤖 Running Playwright UI mode..." "Yellow"
Write-Host ""
Write-Host "Instructions:"
Write-Host "1. The Playwright UI will open"
Write-Host "2. Click on 'tests/download.spec.js'"
Write-Host "3. Click the 'Run' button to start"
Write-Host "4. Monitor the progress in the UI"
Write-Host ""

# Set environment variable
$env:TARGET_DATE = $targetDate

# Create or update .env file
$envContent = @"
# TE Automation Environment Configuration
# Auto-generated by run-download.ps1

# TE Portal Credentials
TE_USERNAME=<EMAIL>
TE_PASSWORD=Editing@1234

# TE Portal URLs
TE_LOGIN_URL=https://production.jow.medknow.com/login
TE_MYTASK_URL=https://production.jow.medknow.com/mytask

# Directory Configuration
INCOMING_DIR=./incoming
DOWNLOADS_DIR=./downloads

# Target Date (Auto-set by PowerShell script)
TARGET_DATE=$targetDate
"@

try {
    $envContent | Out-File -FilePath ".env" -Encoding UTF8
    Write-ColorText "✅ Environment configured with target date: $targetDate" "Green"
    Write-Host ""
}
catch {
    Write-ColorText "⚠️ Warning: Could not create .env file" "Yellow"
    Write-Host ""
}

# Check if node_modules exists
if (-not (Test-Path "node_modules")) {
    Write-ColorText "⚠️ Node modules not found. Installing dependencies..." "Yellow"
    Write-Host ""
    
    try {
        & npm install
        if ($LASTEXITCODE -ne 0) {
            throw "npm install failed"
        }
        Write-ColorText "✅ Dependencies installed successfully!" "Green"
        Write-Host ""
    }
    catch {
        Write-Host ""
        Write-ColorText "❌ ERROR: Failed to install dependencies!" "Red"
        Write-Host "Please run 'npm install' manually."
        Write-Host ""
        Read-Host "Press ENTER to exit"
        exit 1
    }
}

# Run Playwright in UI mode
Write-ColorText "🚀 Launching Playwright UI..." "Green"
Write-Host ""

try {
    & npx playwright test tests/download.spec.js --ui
    
    if ($LASTEXITCODE -ne 0) {
        throw "Playwright execution failed"
    }
    
    Write-Host ""
    Write-ColorText "✅ Playwright UI session completed!" "Green"
    Write-Host ""
    Write-ColorText "📁 Check the 'incoming' folder for downloaded articles:" "Yellow"
    Write-Host "   incoming\$targetDate\"
    Write-Host ""
}
catch {
    Write-Host ""
    Write-ColorText "❌ ERROR: Playwright execution failed!" "Red"
    Write-Host ""
    Write-Host "Troubleshooting tips:"
    Write-Host "1. Make sure you have Node.js installed"
    Write-Host "2. Run 'npm install' to install dependencies"
    Write-Host "3. Check if the TE portal is accessible"
    Write-Host "4. Verify your credentials in the .env file"
    Write-Host ""
}

Write-Host ""
Write-ColorText "========================================" "Cyan"
Write-ColorText "           SESSION COMPLETE" "Cyan"
Write-ColorText "========================================" "Cyan"
Write-Host ""
Read-Host "Press ENTER to exit"
