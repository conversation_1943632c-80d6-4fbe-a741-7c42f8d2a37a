{"version": 3, "file": "querying.js", "sourceRoot": "https://raw.githubusercontent.com/fb55/domutils/96c989e159c98218958f375ea04210f2d1b43c0c/src/", "sources": ["querying.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,KAAK,EAAE,WAAW,EAAoB,MAAM,YAAY,CAAC;AAElE;;;;;;;;;GASG;AACH,MAAM,UAAU,MAAM,CAClB,IAAgC,EAChC,IAAyB,EACzB,OAAO,GAAG,IAAI,EACd,KAAK,GAAG,QAAQ;IAEhB,OAAO,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;AAC3E,CAAC;AAED;;;;;;;;;GASG;AACH,MAAM,UAAU,IAAI,CAChB,IAAgC,EAChC,KAAgB,EAChB,OAAgB,EAChB,KAAa;IAEb,MAAM,MAAM,GAAc,EAAE,CAAC;IAC7B,6CAA6C;IAC7C,MAAM,SAAS,GAAG,CAAC,KAAK,CAAC,CAAC;IAC1B,8CAA8C;IAC9C,MAAM,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC;IAEvB,SAAS;QACL,sEAAsE;QACtE,IAAI,UAAU,CAAC,CAAC,CAAC,IAAI,SAAS,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE;YACtC,qDAAqD;YACrD,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE;gBACzB,OAAO,MAAM,CAAC;aACjB;YAED,sDAAsD;YACtD,SAAS,CAAC,KAAK,EAAE,CAAC;YAClB,UAAU,CAAC,KAAK,EAAE,CAAC;YAEnB,0DAA0D;YAC1D,SAAS;SACZ;QAED,MAAM,IAAI,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QAE3C,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE;YACZ,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClB,IAAI,EAAE,KAAK,IAAI,CAAC;gBAAE,OAAO,MAAM,CAAC;SACnC;QAED,IAAI,OAAO,IAAI,WAAW,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;YAC1D;;;eAGG;YACH,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YACtB,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;SACpC;KACJ;AACL,CAAC;AAED;;;;;;;;GAQG;AACH,MAAM,UAAU,YAAY,CACxB,IAA0B,EAC1B,KAAU;IAEV,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC5B,CAAC;AAED;;;;;;;;GAQG;AACH,MAAM,UAAU,OAAO,CACnB,IAAgC,EAChC,KAAgB,EAChB,OAAO,GAAG,IAAI;IAEd,IAAI,IAAI,GAAG,IAAI,CAAC;IAEhB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE;QAC5C,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QACtB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;YACd,SAAS;SACZ;aAAM,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE;YACnB,IAAI,GAAG,IAAI,CAAC;SACf;aAAM,IAAI,OAAO,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;YAC5C,IAAI,GAAG,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;SAC7C;KACJ;IAED,OAAO,IAAI,CAAC;AAChB,CAAC;AAED;;;;;;;GAOG;AACH,MAAM,UAAU,SAAS,CACrB,IAAgC,EAChC,KAAgB;IAEhB,OAAO,KAAK,CAAC,IAAI,CACb,CAAC,OAAO,EAAE,EAAE,CACR,KAAK,CAAC,OAAO,CAAC;QACd,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,SAAS,CAAC,IAAI,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC,CAC3D,CAAC;AACN,CAAC;AAED;;;;;;;;;GASG;AACH,MAAM,UAAU,OAAO,CACnB,IAAgC,EAChC,KAAgB;IAEhB,MAAM,MAAM,GAAG,EAAE,CAAC;IAClB,MAAM,SAAS,GAAG,CAAC,KAAK,CAAC,CAAC;IAC1B,MAAM,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC;IAEvB,SAAS;QACL,IAAI,UAAU,CAAC,CAAC,CAAC,IAAI,SAAS,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE;YACtC,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;gBACxB,OAAO,MAAM,CAAC;aACjB;YAED,sDAAsD;YACtD,SAAS,CAAC,KAAK,EAAE,CAAC;YAClB,UAAU,CAAC,KAAK,EAAE,CAAC;YAEnB,0DAA0D;YAC1D,SAAS;SACZ;QAED,MAAM,IAAI,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QAE3C,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;YAAE,SAAS;QAC3B,IAAI,IAAI,CAAC,IAAI,CAAC;YAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAElC,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;YAC1B,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YACtB,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;SACpC;KACJ;AACL,CAAC"}