/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { authorizedbuyersmarketplace_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof authorizedbuyersmarketplace_v1.Authorizedbuyersmarketplace;
};
export declare function authorizedbuyersmarketplace(version: 'v1'): authorizedbuyersmarketplace_v1.Authorizedbuyersmarketplace;
export declare function authorizedbuyersmarketplace(options: authorizedbuyersmarketplace_v1.Options): authorizedbuyersmarketplace_v1.Authorizedbuyersmarketplace;
declare const auth: AuthPlus;
export { auth };
export { authorizedbuyersmarketplace_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, GaxiosPromise, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
