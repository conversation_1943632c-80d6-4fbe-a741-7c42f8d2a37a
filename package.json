{"name": "edit<PERSON>", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"download": "node run-download-phase.js", "test:download": "npx playwright test tests/download-phase.spec.js --reporter=line", "check-incoming": "node check-incoming.js", "server": "node server.js", "dev": "node server.js"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@playwright/test": "^1.42.1", "@types/node": "^20.8.9"}, "dependencies": {"adm-zip": "^0.5.12", "cheerio": "^1.0.0-rc.12", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "googleapis": "^128.0.0", "jsonwebtoken": "^9.0.2", "nodemailer": "^6.9.7", "playwright": "^1.42.1", "playwright-extra": "^4.3.6", "puppeteer-extra-plugin-stealth": "^2.11.2"}}