#!/usr/bin/env node

/**
 * TE Download Phase Runner
 * 
 * This script runs only the Download Phase of the TE Automation.
 * It discovers articles with "Yet-to-Start" status and downloads them
 * to the incoming/ directory with metadata JSON files.
 * 
 * Usage:
 *   node run-download-phase.js
 *   
 * Or with npm script:
 *   npm run download
 */

const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 Starting TE Download Phase...\n');

// Run the download phase test
const playwrightProcess = spawn('npx', [
    'playwright', 
    'test', 
    'tests/download-phase.spec.js',
    '--reporter=line'
], {
    stdio: 'inherit',
    cwd: __dirname
});

playwrightProcess.on('close', (code) => {
    if (code === 0) {
        console.log('\n✅ Download Phase completed successfully!');
        console.log('📁 Check the incoming/ directory for downloaded files and metadata.');
    } else {
        console.log(`\n❌ Download Phase failed with exit code ${code}`);
        process.exit(code);
    }
});

playwrightProcess.on('error', (error) => {
    console.error('❌ Failed to start Download Phase:', error.message);
    process.exit(1);
});
