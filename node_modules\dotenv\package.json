{"name": "dotenv", "version": "16.3.1", "description": "Loads environment variables from .env file", "main": "lib/main.js", "types": "lib/main.d.ts", "exports": {".": {"types": "./lib/main.d.ts", "require": "./lib/main.js", "default": "./lib/main.js"}, "./config": "./config.js", "./config.js": "./config.js", "./lib/env-options": "./lib/env-options.js", "./lib/env-options.js": "./lib/env-options.js", "./lib/cli-options": "./lib/cli-options.js", "./lib/cli-options.js": "./lib/cli-options.js", "./package.json": "./package.json"}, "scripts": {"dts-check": "tsc --project tests/types/tsconfig.json", "lint": "standard", "lint-readme": "standard-markdown", "pretest": "npm run lint && npm run dts-check", "test": "tap tests/*.js --100 -Rspec", "prerelease": "npm test", "release": "standard-version"}, "repository": {"type": "git", "url": "git://github.com/motdotla/dotenv.git"}, "funding": "https://github.com/motdotla/dotenv?sponsor=1", "keywords": ["dotenv", "env", ".env", "environment", "variables", "config", "settings"], "readmeFilename": "README.md", "license": "BSD-2-<PERSON><PERSON>", "devDependencies": {"@definitelytyped/dtslint": "^0.0.133", "@types/node": "^18.11.3", "decache": "^4.6.1", "sinon": "^14.0.1", "standard": "^17.0.0", "standard-markdown": "^7.1.0", "standard-version": "^9.5.0", "tap": "^16.3.0", "tar": "^6.1.11", "typescript": "^4.8.4"}, "engines": {"node": ">=12"}, "browser": {"fs": false}}