{"version": 3, "file": "querying.js", "sourceRoot": "https://raw.githubusercontent.com/fb55/domutils/96c989e159c98218958f375ea04210f2d1b43c0c/src/", "sources": ["querying.ts"], "names": [], "mappings": ";;;AAAA,yCAAkE;AAElE;;;;;;;;;GASG;AACH,SAAgB,MAAM,CAClB,IAAgC,EAChC,IAAyB,EACzB,OAAc,EACd,KAAgB;IADhB,wBAAA,EAAA,cAAc;IACd,sBAAA,EAAA,gBAAgB;IAEhB,OAAO,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;AAC3E,CAAC;AAPD,wBAOC;AAED;;;;;;;;;GASG;AACH,SAAgB,IAAI,CAChB,IAAgC,EAChC,KAAgB,EAChB,OAAgB,EAChB,KAAa;IAEb,IAAM,MAAM,GAAc,EAAE,CAAC;IAC7B,6CAA6C;IAC7C,IAAM,SAAS,GAAG,CAAC,KAAK,CAAC,CAAC;IAC1B,8CAA8C;IAC9C,IAAM,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC;IAEvB,SAAS;QACL,sEAAsE;QACtE,IAAI,UAAU,CAAC,CAAC,CAAC,IAAI,SAAS,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE;YACtC,qDAAqD;YACrD,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE;gBACzB,OAAO,MAAM,CAAC;aACjB;YAED,sDAAsD;YACtD,SAAS,CAAC,KAAK,EAAE,CAAC;YAClB,UAAU,CAAC,KAAK,EAAE,CAAC;YAEnB,0DAA0D;YAC1D,SAAS;SACZ;QAED,IAAM,IAAI,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QAE3C,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE;YACZ,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClB,IAAI,EAAE,KAAK,IAAI,CAAC;gBAAE,OAAO,MAAM,CAAC;SACnC;QAED,IAAI,OAAO,IAAI,IAAA,wBAAW,EAAC,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;YAC1D;;;eAGG;YACH,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YACtB,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;SACpC;KACJ;AACL,CAAC;AA5CD,oBA4CC;AAED;;;;;;;;GAQG;AACH,SAAgB,YAAY,CACxB,IAA0B,EAC1B,KAAU;IAEV,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC5B,CAAC;AALD,oCAKC;AAED;;;;;;;;GAQG;AACH,SAAgB,OAAO,CACnB,IAAgC,EAChC,KAAgB,EAChB,OAAc;IAAd,wBAAA,EAAA,cAAc;IAEd,IAAI,IAAI,GAAG,IAAI,CAAC;IAEhB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE;QAC5C,IAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QACtB,IAAI,CAAC,IAAA,kBAAK,EAAC,IAAI,CAAC,EAAE;YACd,SAAS;SACZ;aAAM,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE;YACnB,IAAI,GAAG,IAAI,CAAC;SACf;aAAM,IAAI,OAAO,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;YAC5C,IAAI,GAAG,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;SAC7C;KACJ;IAED,OAAO,IAAI,CAAC;AAChB,CAAC;AAnBD,0BAmBC;AAED;;;;;;;GAOG;AACH,SAAgB,SAAS,CACrB,IAAgC,EAChC,KAAgB;IAEhB,OAAO,KAAK,CAAC,IAAI,CACb,UAAC,OAAO;QACJ,OAAA,IAAA,kBAAK,EAAC,OAAO,CAAC;YACd,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,SAAS,CAAC,IAAI,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC;IADpD,CACoD,CAC3D,CAAC;AACN,CAAC;AATD,8BASC;AAED;;;;;;;;;GASG;AACH,SAAgB,OAAO,CACnB,IAAgC,EAChC,KAAgB;IAEhB,IAAM,MAAM,GAAG,EAAE,CAAC;IAClB,IAAM,SAAS,GAAG,CAAC,KAAK,CAAC,CAAC;IAC1B,IAAM,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC;IAEvB,SAAS;QACL,IAAI,UAAU,CAAC,CAAC,CAAC,IAAI,SAAS,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE;YACtC,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;gBACxB,OAAO,MAAM,CAAC;aACjB;YAED,sDAAsD;YACtD,SAAS,CAAC,KAAK,EAAE,CAAC;YAClB,UAAU,CAAC,KAAK,EAAE,CAAC;YAEnB,0DAA0D;YAC1D,SAAS;SACZ;QAED,IAAM,IAAI,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QAE3C,IAAI,CAAC,IAAA,kBAAK,EAAC,IAAI,CAAC;YAAE,SAAS;QAC3B,IAAI,IAAI,CAAC,IAAI,CAAC;YAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAElC,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;YAC1B,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YACtB,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;SACpC;KACJ;AACL,CAAC;AAhCD,0BAgCC"}