const fs = require('fs');
const { google } = require('googleapis');

const apikeys = require('./apikeys.json');
const SCOPE = ['https://www.googleapis.com/auth/drive'];

// A Function that can provide access to google drive api
async function authorize() {
    const jwtClient = new google.auth.JWT(
        apikeys.client_email,
        null,
        apikeys.private_key,
        SCOPE
    );

    await jwtClient.authorize();

    return jwtClient;
}

// A Function that will upload the desired file to google drive folder
async function uploadFile(authClient, path) {
    return new Promise((resolve, rejected) => {
        const drive = google.drive({ version: 'v3', auth: authClient });

        var fileMetaData = {
            name: `text-${new Date().toLocaleString()}.`,
            parents: ['1xHhAfSIgcX5k7LQStSvszlQmN7OP52di'] // A folder ID to which file will get uploaded
        }

        drive.files.create({
            resource: fileMetaData,
            media: {
                body: fs.createReadStream(path), // files that will get uploaded
                mimeType: 'application/x-rar-compressed'
            },
            fields: 'id'
        }, function (error, file) {
            if (error) {
                return rejected(error)
            }
            const url = `https://drive.google.com/file/d/${file.data.id}/view?usp=drive_link`;
            console.log('file uploaded', url);
            resolve(file);
            return url;
        })
    });
}


const filePath = async (path) => {
    console.log("File path", path);
    await authorize().then((p) => uploadFile(p, path)).catch("error", console.error());

}

export default { filePath }